<?php
namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Rate;

use Magento\Quote\Model\Quote\Address\RateResult\Method;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Magento\Framework\App\State;
use Magento\Framework\App\Area;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Backend\Model\Session\Quote as BackendQuoteSession;
use Psr\Log\LoggerInterface;

/**
 * Plugin to automatically inject threshold subtitle for free shipping methods
 */
class ResultPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var State
     */
    protected $appState;

    /**
     * @var CheckoutSession
     */
    protected $checkoutSession;

    /**
     * @var BackendQuoteSession
     */
    protected $backendQuoteSession;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var array
     */
    protected $processedRates = [];

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param State $appState
     * @param CheckoutSession $checkoutSession
     * @param BackendQuoteSession $backendQuoteSession
     * @param LoggerInterface $logger
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        State $appState,
        CheckoutSession $checkoutSession,
        BackendQuoteSession $backendQuoteSession,
        LoggerInterface $logger
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->appState = $appState;
        $this->checkoutSession = $checkoutSession;
        $this->backendQuoteSession = $backendQuoteSession;
        $this->logger = $logger;
    }

    /**
     * Automatically inject threshold subtitle for free shipping methods
     *
     * @param Method $subject
     * @param string $result
     * @return string
     */
    public function afterGetMethodTitle(Method $subject, $result)
    {
        // Only process free shipping methods
        if ($subject->getCarrier() !== 'freeshipping') {
            return $result;
        }

        // Avoid processing the same rate multiple times
        $rateId = $subject->getCarrier() . '_' . $subject->getMethod();
        if (isset($this->processedRates[$rateId])) {
            return $result;
        }

        // Check if subtitle is already present
        if (strpos($result, '- For orders over') !== false) {
            return $result;
        }

        try {
            $quote = $this->getQuote();
            if (!$quote) {
                return $result;
            }

            $country = $quote->getShippingAddress()->getCountryId();
            $subtotal = $quote->getBaseSubtotalWithDiscount();

            $threshold = $this->getFreeShippingThresholdForCountry($country, $subtotal);
            if ($threshold !== null) {
                $result .= sprintf(' - For orders over $%.2f', $threshold);
                $this->processedRates[$rateId] = true;
            }
        } catch (\Exception $e) {
            $this->logger->error('Error adding free shipping threshold subtitle: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Get the current quote based on the area
     *
     * @return \Magento\Quote\Model\Quote|null
     */
    protected function getQuote()
    {
        try {
            $areaCode = $this->appState->getAreaCode();
            
            if ($areaCode === Area::AREA_ADMINHTML) {
                return $this->backendQuoteSession->getQuote();
            } else {
                return $this->checkoutSession->getQuote();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error getting quote in rate plugin: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get free shipping threshold for the given country that is met
     *
     * @param string $country
     * @param float $subtotal
     * @return float|null
     */
    protected function getFreeShippingThresholdForCountry($country, $subtotal)
    {
        if (!$country || $subtotal <= 0) {
            return null;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
                   ->addFieldToFilter('countries', ['like' => "%{$country}%"])
                   ->setOrder('min_order_amount', 'ASC');

        $threshold = $collection->getFirstItem();
        
        return $threshold->getId() ? $threshold->getMinOrderAmount() : null;
    }
}
