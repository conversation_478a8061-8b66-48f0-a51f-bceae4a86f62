<?php
namespace Coditron\CustomShippingRate\Plugin\Block\Adminhtml\Order\Create\Shipping\Method;

use Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Method\Form;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
use Psr\Log\LoggerInterface;

/**
 * Plugin to ensure free shipping threshold subtitle is always added in admin order creation
 */
class FormPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        LoggerInterface $logger
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->logger = $logger;
    }

    /**
     * Add threshold subtitle to free shipping rates in admin order creation
     *
     * @param Form $subject
     * @param array $result
     * @return array
     */
    public function afterGetGroupShippingRates(Form $subject, $result)
    {
        if (!isset($result['freeshipping']) || empty($result['freeshipping'])) {
            return $result;
        }

        try {
            $quote = $subject->getQuote();
            if (!$quote) {
                return $result;
            }

            $country = $quote->getShippingAddress()->getCountryId();
            $subtotal = $quote->getBaseSubtotalWithDiscount();

            $this->logger->info('Admin Form Plugin - Processing free shipping rates', [
                'country' => $country,
                'subtotal' => $subtotal,
                'rates_count' => count($result['freeshipping'])
            ]);

            $threshold = $this->getFreeShippingThresholdForCountry($country, $subtotal);
            
            if ($threshold !== null) {
                foreach ($result['freeshipping'] as $rate) {
                    $currentTitle = $rate->getMethodTitle();
                    if (strpos($currentTitle, '- For orders over') === false) {
                        $newTitle = $currentTitle . sprintf(' - For orders over $%.2f', $threshold);
                        $rate->setMethodTitle($newTitle);
                        
                        $this->logger->info('Admin Form Plugin - Updated rate title', [
                            'original' => $currentTitle,
                            'new' => $newTitle,
                            'threshold' => $threshold
                        ]);
                    }
                }
            } else {
                $this->logger->info('Admin Form Plugin - No threshold found', [
                    'country' => $country,
                    'subtotal' => $subtotal
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error in admin form plugin: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * Get free shipping threshold for the given country that is met
     *
     * @param string $country
     * @param float $subtotal
     * @return float|null
     */
    protected function getFreeShippingThresholdForCountry($country, $subtotal)
    {
        if (!$country || $subtotal <= 0) {
            return null;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
                   ->addFieldToFilter('countries', ['like' => "%{$country}%"])
                   ->setOrder('min_order_amount', 'ASC');

        $threshold = $collection->getFirstItem();
        
        return $threshold->getId() ? $threshold->getMinOrderAmount() : null;
    }
}
